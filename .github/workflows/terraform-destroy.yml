name: Terraform Destroy

on:
  workflow_dispatch:
    inputs:
      cluster:
        description: 'Cluster to destroy (leave empty for all)'
        required: false
        type: string
      environment:
        description: 'Environment to use'
        required: false
        type: choice
        options:
          - dev
          - prod
        default: 'dev'
      confirm_destroy:
        description: 'Type "DESTROY" to confirm destruction'
        required: true
        type: string

permissions:
  contents: read

jobs:
  terraform-destroy:
    name: Terraform Destroy
    runs-on: ubuntu-latest
    strategy:
      matrix:
        cluster: ${{ github.event.inputs.cluster && fromJSON(format('["{}"]', github.event.inputs.cluster)) || fromJSON('["main"]') }}

    env:
      TF_VAR_hcloud_token: ${{ secrets.HCLOUD_TOKEN }}
      TF_VAR_ssh_public_key: ${{ secrets.SSH_PUBLIC_KEY }}
      TF_VAR_ssh_private_key: ${{ secrets.SSH_PRIVATE_KEY }}
      TF_VAR_hcloud_ssh_key_id: ${{ secrets.HCLOUD_SSH_KEY_ID }}
      TF_VAR_ssh_additional_public_keys: ${{ secrets.SSH_ADDITIONAL_PUBLIC_KEYS }}
      # TF_VAR_firewall_ssh_source_ip: ${{ secrets.FIREWALL_SSH_SOURCE_IP }}
      TF_TOKEN_app_terraform_io: ${{ secrets.TF_API_TOKEN }}

    steps:
      - name: Validate Destroy Confirmation
        run: |
          if [ "${{ github.event.inputs.confirm_destroy }}" != "DESTROY" ]; then
            echo "❌ Destroy confirmation failed. You must type 'DESTROY' exactly to proceed."
            exit 1
          fi
          echo "✅ Destroy confirmation validated"

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3

      - name: Terraform Init
        id: init
        run: terraform init --upgrade
        working-directory: terraform/${{ matrix.cluster }}

      - name: Terraform Validate
        id: validate
        run: terraform validate
        working-directory: terraform/${{ matrix.cluster }}

      - name: Terraform Plan Destroy
        id: plan-destroy
        run: terraform plan -destroy -no-color
        working-directory: terraform/${{ matrix.cluster }}
        continue-on-error: true

      - name: Terraform Plan Destroy Status
        if: steps.plan-destroy.outcome == 'failure'
        run: exit 1

      - name: Terraform Destroy
        id: destroy
        run: terraform destroy -auto-approve
        working-directory: terraform/${{ matrix.cluster }}
